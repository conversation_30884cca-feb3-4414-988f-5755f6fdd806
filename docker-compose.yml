version: '3.8'
services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: todo-backend
    restart: on-failure
    working_dir: /app/backend
    volumes:
      - ./backend:/app/backend
      - /app/backend/node_modules
      - uploads-data:/app/backend/public/uploads
    ports:
      - "5000:5000"
    command: >
      bash -c "
        npm install &&
        if [ \"$$NODE_ENV\" = \"production\" ]; then
          npm run prod;
        else
          npm start;
        fi
      "
    depends_on:
      - mongo
    env_file:
      - ./backend/.env

  mongo:
    image: mongo:5.0
    container_name: mongo
    restart: on-failure
    ports:
      - "27017:27017"
    volumes:
      - mongo-data:/data/db

volumes:
  mongo-data:
  uploads-data:
