const express = require('express');
const router = express.Router();
const Task = require('../models/task');
const Category = require('../models/category');
const auth = require('../middlewares/auth');
const { restrictGuest, canModifyTask } = require('../middlewares/taskPermissions');

// Apply authentication middleware
router.use(auth);

// Get all tasks for the current user with pagination and filtering
router.get('/tasks', async (req, res) => {
    try {
        const {
            page = 1,
            limit = 50,
            category,
            status,
            sortBy = 'date',
            sortOrder = 'asc'
        } = req.query;

        // Build query
        const query = { user: req.user._id };

        if (category && category !== 'all') {
            query.categoryChoosed = category;
        }

        if (status && status !== 'all') {
            query.completed = status === 'completed';
        }

        // Build sort object
        const sort = {};
        sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Add secondary sort by creation date
        if (sortBy !== 'createdAt') {
            sort.createdAt = -1;
        }

        // Execute query with pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const tasks = await Task.find(query)
            .populate('category', 'name icon color')
            .sort(sort)
            .skip(skip)
            .limit(parseInt(limit))
            .lean(); // Use lean() for better performance

        // Get total count for pagination
        const totalTasks = await Task.countDocuments(query);
        const totalPages = Math.ceil(totalTasks / parseInt(limit));

        res.json({
            tasks,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalTasks,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1
            }
        });
    } catch (error) {
        console.error('API - Error fetching tasks:', error);
        res.status(500).json({ error: 'Failed to fetch tasks' });
    }
});

// Get task statistics for dashboard
router.get('/tasks/stats', async (req, res) => {
    try {
        const userId = req.user._id;

        // Use aggregation for better performance
        const stats = await Task.aggregate([
            { $match: { user: userId } },
            {
                $group: {
                    _id: {
                        category: '$categoryChoosed',
                        completed: '$completed'
                    },
                    count: { $sum: 1 }
                }
            },
            {
                $group: {
                    _id: '$_id.category',
                    total: { $sum: '$count' },
                    completed: {
                        $sum: {
                            $cond: [{ $eq: ['$_id.completed', true] }, '$count', 0]
                        }
                    }
                }
            }
        ]);

        // Format the response
        const categoryStats = {};
        let totalTasks = 0;
        let completedTasks = 0;

        stats.forEach(stat => {
            const category = stat._id || 'others';
            categoryStats[category] = {
                total: stat.total,
                completed: stat.completed,
                pending: stat.total - stat.completed,
                completionRate: stat.total > 0 ? Math.round((stat.completed / stat.total) * 100) : 0
            };
            totalTasks += stat.total;
            completedTasks += stat.completed;
        });

        res.json({
            overview: {
                total: totalTasks,
                completed: completedTasks,
                pending: totalTasks - completedTasks,
                completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
            },
            categories: categoryStats
        });
    } catch (error) {
        console.error('API - Error fetching task stats:', error);
        res.status(500).json({ error: 'Failed to fetch task statistics' });
    }
});

// Get pending tasks
router.get('/tasks/pending', async (req, res) => {
    try {
        const tasks = await Task.find({
            user: req.user._id,
            completed: false
        })
            .populate('category')
            .sort({ date: 1, time: 1 });

        res.json(tasks);
    } catch (error) {
        console.error('API - Error fetching pending tasks:', error);
        res.status(500).json({ error: 'Failed to fetch pending tasks' });
    }
});

// Get completed tasks
router.get('/tasks/completed', async (req, res) => {
    try {
        const tasks = await Task.find({
            user: req.user._id,
            completed: true
        })
            .populate('category')
            .sort({ date: -1, time: -1 });

        res.json(tasks);
    } catch (error) {
        console.error('API - Error fetching completed tasks:', error);
        res.status(500).json({ error: 'Failed to fetch completed tasks' });
    }
});

// Get task by ID
router.get('/tasks/:id', async (req, res) => {
    try {
        const task = await Task.findOne({
            _id: req.params.id,
            user: req.user._id
        }).populate('category');

        if (!task) {
            return res.status(404).json({ error: 'Task not found' });
        }

        res.json(task);
    } catch (error) {
        console.error('API - Error fetching task:', error);
        res.status(500).json({ error: 'Failed to fetch task' });
    }
});

// Create a new task
router.post('/tasks', restrictGuest, async (req, res) => {
    try {
        const { title, description, date, time, categoryChoosed, category } = req.body;

        // Validate required fields
        if (!title || !date || !categoryChoosed) {
            return res.status(400).json({ error: 'Title, date, and category are required' });
        }

        // Create the task
        const task = new Task({
            title,
            description,
            date,
            time,
            categoryChoosed,
            category: category || null,
            user: req.user._id,
        });

        await task.save();

        // Return the created task
        res.status(201).json(task);
    } catch (error) {
        console.error('API - Error creating task:', error);
        res.status(500).json({ error: 'Failed to create task' });
    }
});

// Update a task
router.put('/tasks/:id', restrictGuest, canModifyTask, async (req, res) => {
    try {
        const { title, description, date, time, categoryChoosed, category } = req.body;

        // Find the existing task first
        const existingTask = await Task.findOne({
            _id: req.params.id,
            user: req.user._id
        });

        if (!existingTask) {
            return res.status(404).json({ error: 'Task not found' });
        }

        // Validate required fields - use existing values if not provided
        const finalTitle = title || existingTask.title;
        const finalDate = date || existingTask.date;
        const finalCategoryChoosed = categoryChoosed || existingTask.categoryChoosed;

        if (!finalTitle || !finalDate || !finalCategoryChoosed) {
            return res.status(400).json({ error: 'Title, date, and category are required' });
        }

        // Update only the provided fields, keep existing values for others
        const updateData = {
            title: finalTitle,
            description: description !== undefined ? description : existingTask.description,
            date: finalDate,
            time: time !== undefined ? time : existingTask.time,
            categoryChoosed: finalCategoryChoosed,
            category: category !== undefined ? category : existingTask.category
        };

        // Find and update the task
        const task = await Task.findOneAndUpdate(
            { _id: req.params.id, user: req.user._id },
            updateData,
            { new: true }
        );

        if (!task) {
            return res.status(404).json({ error: 'Task not found' });
        }

        res.json(task);
    } catch (error) {
        console.error('API - Error updating task:', error);
        res.status(500).json({ error: 'Failed to update task' });
    }
});

// Toggle task status
router.post('/tasks/:id/toggle-status', restrictGuest, canModifyTask, async (req, res) => {
    try {
        const task = await Task.findOne({ _id: req.params.id, user: req.user._id });

        if (!task) {
            return res.status(404).json({ error: 'Task not found' });
        }

        // Toggle the completed status
        task.completed = !task.completed;
        // Update status field to match completed boolean
        task.status = task.completed ? 'completed' : 'pending';
        await task.save();

        res.json(task);
    } catch (error) {
        console.error('API - Error toggling task status:', error);
        res.status(500).json({ error: 'Failed to toggle task status' });
    }
});

// Delete a task
router.delete('/tasks/:id', restrictGuest, canModifyTask, async (req, res) => {
    try {
        const result = await Task.deleteOne({ _id: req.params.id, user: req.user._id });

        if (result.deletedCount === 0) {
            return res.status(404).json({ error: 'Task not found' });
        }

        res.json({ success: true });
    } catch (error) {
        console.error('API - Error deleting task:', error);
        res.status(500).json({ error: 'Failed to delete task' });
    }
});

module.exports = router;
