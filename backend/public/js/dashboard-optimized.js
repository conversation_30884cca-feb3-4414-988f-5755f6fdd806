/**
 * Optimized Dashboard JavaScript
 * Handles task loading, filtering, and interactions with improved performance
 */

class TaskDashboard {
    constructor() {
        this.currentPage = 1;
        this.currentCategory = 'all';
        this.currentStatus = 'all';
        this.tasksPerPage = 20;
        this.isLoading = false;
        this.cache = new Map();
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadInitialData();
    }

    bindEvents() {
        // Filter events
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-filter]')) {
                this.handleFilterChange(e.target);
            }
        });

        // Task action events
        document.addEventListener('click', (e) => {
            if (e.target.matches('.show-more-btn')) {
                this.loadMoreTasks(e.target.dataset.category);
            }
            
            if (e.target.matches('.task-action')) {
                this.handleTaskAction(e.target);
            }
        });

        // Form submission events
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.task-form')) {
                this.handleTaskSubmission(e);
            }
        });

        // Search functionality
        const searchInput = document.querySelector('#task-search');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.handleSearch(e.target.value);
                }, 300);
            });
        }
    }

    async loadInitialData() {
        try {
            this.showLoading();
            
            // Load tasks and stats in parallel
            const [tasksResponse, statsResponse] = await Promise.all([
                this.fetchTasks(),
                this.fetchStats()
            ]);

            this.renderTasks(tasksResponse.tasks);
            this.renderStats(statsResponse);
            this.updatePagination(tasksResponse.pagination);
            
        } catch (error) {
            this.showError('Failed to load dashboard data');
            console.error('Dashboard load error:', error);
        } finally {
            this.hideLoading();
        }
    }

    async fetchTasks(options = {}) {
        const params = new URLSearchParams({
            page: options.page || this.currentPage,
            limit: options.limit || this.tasksPerPage,
            category: options.category || this.currentCategory,
            status: options.status || this.currentStatus,
            sortBy: options.sortBy || 'date',
            sortOrder: options.sortOrder || 'asc'
        });

        const cacheKey = params.toString();
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }

        const response = await fetch(`/api/tasks?${params}`);
        if (!response.ok) {
            throw new Error('Failed to fetch tasks');
        }

        const data = await response.json();
        this.cache.set(cacheKey, data);
        
        // Clear cache after 5 minutes
        setTimeout(() => this.cache.delete(cacheKey), 5 * 60 * 1000);
        
        return data;
    }

    async fetchStats() {
        const response = await fetch('/api/tasks/stats');
        if (!response.ok) {
            throw new Error('Failed to fetch stats');
        }
        return response.json();
    }

    renderTasks(tasks) {
        const container = document.querySelector('#tasks-container');
        if (!container) return;

        if (tasks.length === 0) {
            container.innerHTML = this.getEmptyStateHTML();
            return;
        }

        // Group tasks by category
        const tasksByCategory = this.groupTasksByCategory(tasks);
        
        container.innerHTML = Object.entries(tasksByCategory)
            .map(([category, categoryTasks]) => 
                this.getCategoryHTML(category, categoryTasks)
            ).join('');

        // Add intersection observer for lazy loading
        this.setupLazyLoading();
    }

    groupTasksByCategory(tasks) {
        return tasks.reduce((groups, task) => {
            const category = task.categoryChoosed || 'others';
            if (!groups[category]) {
                groups[category] = [];
            }
            groups[category].push(task);
            return groups;
        }, {});
    }

    getCategoryHTML(category, tasks) {
        const categoryInfo = this.getCategoryInfo(category);
        const completedCount = tasks.filter(task => task.completed).length;
        const progressPercent = Math.round((completedCount / tasks.length) * 100);

        return `
            <div class="category-section" data-category="${category}">
                <div class="category-header">
                    <div class="category-info">
                        <img src="${categoryInfo.icon}" alt="${categoryInfo.name}" class="category-icon" loading="lazy">
                        <div class="category-details">
                            <h3 class="category-name">${categoryInfo.name}</h3>
                            <span class="category-count">
                                ${tasks.length} task${tasks.length !== 1 ? 's' : ''}
                                ${completedCount > 0 ? `• ${completedCount} completed` : ''}
                            </span>
                        </div>
                    </div>
                    <div class="category-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${progressPercent}%; background-color: ${categoryInfo.color};"></div>
                        </div>
                        <span class="progress-text">${progressPercent}%</span>
                    </div>
                </div>
                <div class="tasks-list">
                    ${tasks.slice(0, 5).map(task => this.getTaskHTML(task)).join('')}
                    ${tasks.length > 5 ? `
                        <div class="show-more">
                            <button class="btn-link show-more-btn" data-category="${category}">
                                Show ${tasks.length - 5} more tasks
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    getTaskHTML(task) {
        return `
            <div class="task-item ${task.completed ? 'completed' : ''}" data-task-id="${task._id}">
                <div class="task-content">
                    <div class="task-main">
                        <h4 class="task-title">${this.escapeHtml(task.title)}</h4>
                        ${task.description ? `
                            <p class="task-description">
                                ${this.escapeHtml(task.description.substring(0, 100))}
                                ${task.description.length > 100 ? '...' : ''}
                            </p>
                        ` : ''}
                    </div>
                    <div class="task-meta">
                        <span class="task-date">
                            <i class="fas fa-calendar"></i>
                            ${task.date}
                            ${task.time ? `<span class="task-time">${task.time}</span>` : ''}
                        </span>
                        ${task.currentDate ? `
                            <span class="task-created">Created: ${task.currentDate}</span>
                        ` : ''}
                    </div>
                </div>
                <div class="task-actions">
                    ${task.completed ? `
                        <button class="btn-icon btn-undo task-action" data-action="toggle" data-task-id="${task._id}" title="Mark as pending">
                            <i class="fas fa-undo"></i>
                        </button>
                    ` : `
                        <button class="btn-icon btn-complete task-action" data-action="toggle" data-task-id="${task._id}" title="Mark as completed">
                            <i class="fas fa-check"></i>
                        </button>
                    `}
                    <a href="/tasks/${task._id}" class="btn-icon btn-view" title="View details">
                        <i class="fas fa-eye"></i>
                    </a>
                    <button class="btn-icon btn-delete task-action" data-action="delete" data-task-id="${task._id}" title="Delete task">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    renderStats(stats) {
        const summaryContainer = document.querySelector('.task-summary');
        if (summaryContainer && stats.overview) {
            summaryContainer.innerHTML = `
                <span class="summary-item">
                    <strong>${stats.overview.total}</strong> Total
                </span>
                <span class="summary-item">
                    <strong>${stats.overview.pending}</strong> Pending
                </span>
                <span class="summary-item">
                    <strong>${stats.overview.completed}</strong> Completed
                </span>
            `;
        }
    }

    async handleTaskAction(element) {
        const action = element.dataset.action;
        const taskId = element.dataset.taskId;

        if (action === 'delete') {
            if (!confirm('Are you sure you want to delete this task?')) {
                return;
            }
        }

        try {
            element.disabled = true;
            element.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';

            let response;
            if (action === 'toggle') {
                response = await fetch(`/api/tasks/${taskId}/toggle-status`, {
                    method: 'POST'
                });
            } else if (action === 'delete') {
                response = await fetch(`/api/tasks/${taskId}`, {
                    method: 'DELETE'
                });
            }

            if (!response.ok) {
                throw new Error('Action failed');
            }

            // Clear cache and reload
            this.cache.clear();
            await this.loadInitialData();
            
            this.showNotification(
                action === 'delete' ? 'Task deleted successfully' : 'Task updated successfully',
                'success'
            );

        } catch (error) {
            this.showNotification('Action failed. Please try again.', 'error');
            console.error('Task action error:', error);
        }
    }

    getCategoryInfo(category) {
        const categories = {
            work: { 
                name: 'Work', 
                icon: 'https://cdn-icons-png.flaticon.com/512/3281/3281289.png',
                color: '#4CAF50'
            },
            personal: { 
                name: 'Personal', 
                icon: 'https://cdn-icons-png.flaticon.com/512/1077/1077012.png',
                color: '#2196F3'
            },
            shopping: { 
                name: 'Shopping', 
                icon: 'https://cdn-icons-png.flaticon.com/512/4290/4290854.png',
                color: '#FF9800'
            },
            others: { 
                name: 'Others', 
                icon: 'https://cdn-icons-png.flaticon.com/512/7245/7245102.png',
                color: '#9C27B0'
            }
        };
        return categories[category] || categories.others;
    }

    getEmptyStateHTML() {
        return `
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h3>No tasks yet</h3>
                <p>Start by creating your first task to get organized!</p>
                <a href="/tasks/manage" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Create Task
                </a>
            </div>
        `;
    }

    showLoading() {
        this.isLoading = true;
        const container = document.querySelector('#tasks-container');
        if (container) {
            container.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Loading tasks...</div>';
        }
    }

    hideLoading() {
        this.isLoading = false;
    }

    showNotification(message, type = 'info') {
        // Implementation depends on your notification system
        console.log(`${type.toUpperCase()}: ${message}`);
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    setupLazyLoading() {
        // Implement intersection observer for images and additional content
        const images = document.querySelectorAll('img[loading="lazy"]');
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }
    }
}

// Initialize dashboard when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    new TaskDashboard();
});
