

/* Sidebar */
.sidebar {
    background-color: var(--dark-bg);
    border-radius: 10px;
    padding: 20px 0;
    box-shadow: var(--card-shadow);
    height: 100%;
    overflow-y: auto;
}

.sidebar-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-links a {
    display: block;
    padding: 12px 20px;
    color: var(--light-text);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.sidebar-links a:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.sidebar-links a.active {
    background-color: rgba(35, 150, 179, 0.2);
    border-left-color: var(--primary-color);
}

.sidebar-links li {
    margin: 0;
    font-weight: 500;
}

/* Main Content Section */
.main-section {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    overflow-y: auto;
    padding-right: 10px;
}

/* Task Management Header */
.task-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.task-management-header h1 {
    color: var(--light-text);
    font-size: 1.8rem;
    margin: 0;
    font-weight: 600;
}

.task-actions {
    display: flex;
    gap: 10px;
}

/* Filters */
.task-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: var(--dark-bg);
    border-radius: 10px;
    box-shadow: var(--card-shadow);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.filter-group label {
    font-size: 0.9rem;
    color: var(--light-text);
    font-weight: 500;
    margin: 5px 0;
    text-align: center;
}

.filter-group select {
    padding: 10px 12px;
    border-radius: 6px;
    background-color: var(--darker-bg);
    color: var(--light-text);
    border: 1px solid var(--border-color);
    min-width: 160px;
    font-size: 0.9rem;
    transition: var(--transition);
}

.filter-group select:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(35, 150, 179, 0.2);
}

/* Task Table */
.task-list-container {
    background-color: var(--dark-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    overflow-x: auto;
    flex-grow: 1;
}

.task-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    color: var(--light-text);
}

.task-table th,
.task-table td {
    padding: 14px 15px;
    text-align: left;
}

.task-table th {
    background-color: var(--darker-bg);
    font-weight: 600;
    color: var(--light-text);
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 2px solid var(--border-color);
}

.task-table th:first-child {
    border-top-left-radius: 8px;
}

.task-table th:last-child {
    border-top-right-radius: 8px;
}

.task-table tbody tr {
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.task-table tbody tr:last-child {
    border-bottom: none;
}

.task-table tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.completed-task {
    opacity: 0.8;
    background-color: rgba(76, 175, 80, 0.05);
}

.completed-task td:not(:last-child) {
    text-decoration: line-through;
    color: rgba(207, 209, 208, 0.7);
}

/* Category and Status Badges */
.category-badge {
    display: inline-block;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: capitalize;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.category-badge.work {
    background-color: #2196F3;
    color: white;
}

.category-badge.personal {
    background-color: #9C27B0;
    color: white;
}

.category-badge.shopping {
    background-color: #FF9800;
    color: white;
}

.category-badge.others {
    background-color: #607D8B;
    color: white;
}

.status-badge {
    display: inline-block;
    padding: 6px 10px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.status-badge.pending {
    background-color: var(--warning-color);
    color: #333;
}

.status-badge.completed {
    background-color: var(--secondary-color);
    color: white;
}

/* Action Buttons */
.actions {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-icon {
    background: none;
    border: none;
    color: var(--light-text);
    cursor: pointer;
    font-size: 1.1rem;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.btn-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.toggle-status {
    color: var(--secondary-color);
}

.toggle-status[data-status="true"] {
    color: var(--warning-color);
}

.edit-task {
    color: var(--primary-color);
}

.delete-task {
    color: var(--danger-color);
}

/* Loading and Empty States */
.loading-row td,
.no-tasks td {
    text-align: center;
    padding: 30px;
    color: var(--light-text);
    font-style: italic;
    background-color: rgba(255, 255, 255, 0.02);
}

/* Aside Section */
.aside-section {
    background-color: var(--dark-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--card-shadow);
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
    overflow-y: auto;
}

/* Profile Section */
.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.username-section {
    font-size: 1.2rem;
    color: var(--light-text);
    margin-bottom: 10px;
    font-weight: 500;
}

.userimage {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.userimage img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-color);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    transition: var(--transition);
}

.userimage img:hover {
    transform: scale(1.05);
}

/* Task Info Section */
.profile-task-info-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 10px;
}

.profile-total-tasks-info,
.profile-completed-tasks-info {
    background-color: var(--darker-bg);
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.profile-total-tasks-info:hover,
.profile-completed-tasks-info:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.total-tasks-info-heading h5,
.completed-tasks-info-heading h5 {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: var(--light-text);
    font-weight: 500;
    font-size: 0.8rem;
    /* text-transform: uppercase; */
}

.total-tasks-info-number h5,
.completed-tasks-info-number h5 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.profile-total-tasks-info .total-tasks-info-number h5 {
    color: var(--primary-color);
}

.profile-completed-tasks-info .completed-tasks-info-number h5 {
    color: var(--secondary-color);
}

/* Category Info Section */
.category-info-section {
    background-color: var(--darker-bg);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.category-info-heading h5 {
    margin: 0 0 15px 0;
    font-size: 1rem;
    color: var(--light-text);
    font-weight: 600;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 8px;
}

/* Category Summary */
.category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 8px;
    transition: var(--transition);
    background-color: rgba(255, 255, 255, 0.03);
}

.category-item:hover {
    background-color: rgba(255, 255, 255, 0.08);
    transform: translateX(3px);
}

.category-name {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.category-count {
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
}

.category-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-icon.work {
    color: #2196F3;
}

.category-icon.personal {
    color: #9C27B0;
}

.category-icon.shopping {
    color: #FF9800;
}

.category-icon.others {
    color: #607D8B;
}

/* Button Styles */
.btn {
    padding: 10px 16px;
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-hover);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--light-text);
}

.btn-outline:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: var(--primary-color);
}

/* Loading Categories */
.loading-categories {
    text-align: center;
    padding: 15px;
    color: var(--light-text);
    font-style: italic;
}

.category-item:last-child {
    border-bottom: none;
}

.category-count {
    font-weight: 600;
    color: #cfd1d0;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.8);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: var(--dark-bg);
    margin: 5% auto;
    padding: 25px;
    border-radius: 12px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    color: var(--light-text);
    animation: slideDown 0.3s ease;
    transform: translateY(0);
}

@keyframes slideDown {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--light-text);
}

.close-modal,
.close-delete-modal {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-modal:hover,
.close-delete-modal:hover {
    color: #fff;
    background-color: rgba(255, 255, 255, 0.1);
}

.input-field {
    margin-bottom: 20px;
}

.input-field label {
    display: block;
    margin-bottom: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--light-text);
}

.input-field input,
.input-field select,
.input-field textarea {
    /* width: 100%; */
    padding: 12px 15px;
    border-radius: 8px;
    background-color: var(--darker-bg);
    border: 1px solid var(--border-color);
    color: var(--light-text);
    font-size: 0.95rem;
    transition: var(--transition);
}

.input-field input:focus,
.input-field select:focus,
.input-field textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(35, 150, 179, 0.2);
}

.input-field textarea {
    resize: vertical;
    min-height: 120px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 25px;
}

/* Delete Confirmation Modal */
.delete-modal-content {
    text-align: center;
    padding: 30px;
}

.delete-modal-content p {
    margin-bottom: 25px;
    font-size: 1.2rem;
    line-height: 1.5;
    color: var(--light-text);
}

.delete-modal-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
}

/* Notification */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1001;
    animation: slideIn 0.3s ease, fadeOut 0.3s ease 2.7s;
    opacity: 0;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

.notification.success {
    background-color: var(--secondary-color);
}

.notification.error {
    background-color: var(--danger-color);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .main {
        grid-template-columns: 200px 1fr 250px;
    }
}

@media (max-width: 992px) {
    .main {
        grid-template-columns: 180px 1fr;
    }

    .aside-section {
        display: none;
    }
}

@media (max-width: 768px) {
    .main {
        grid-template-columns: 1fr;
    }

    .sidebar {
        display: none;
    }

    .task-filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
        padding: 20px;
    }
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border-radius: 5px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
}

.btn-secondary {
    background-color: #2196F3;
    color: white;
}

.btn-secondary:hover {
    background-color: #0b7dda;
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
}

.btn-outline {
    background-color: transparent;
    color: #cfd1d0;
    border: 1px solid #cfd1d0;
}

.btn-outline:hover {
    background-color: #333;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

/* Responsive Styles */
@media screen and (max-width: 768px) {
    .task-management-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .task-filters {
        flex-direction: column;
    }

    .filter-group {
        width: 100%;
    }

    .task-table th:nth-child(3),
    .task-table td:nth-child(3) {
        display: none;
    }
}

@media screen and (max-width: 480px) {
    .task-table th:nth-child(2),
    .task-table td:nth-child(2) {
        display: none;
    }

    .actions {
        flex-direction: column;
    }
}
