
/* Task Management Styles */
:root {
    --primary-color: #2396b3;
    --primary-hover: #1a7a8f;
    --secondary-color: #4CAF50;
    --secondary-hover: #45a049;
    --danger-color: #f44336;
    --danger-hover: #d32f2f;
    --warning-color: #FFC107;
    --warning-hover: #e6ac00;
    --dark-bg: #1d2021;
    --darker-bg: #181a1b;
    --light-text: #cfd1d0;
    --border-color: #333;
    --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    --transition: all 0.3s ease;
}

/* Font imports and base styles are now in common.css */

body {
    margin: 0;
    padding: 0;
    width: 100vw;
    height: 100vh;
    color: #cfd1d0;
    background-color: #181a1b;
    overflow-x: hidden;
    box-sizing: border-box;
}

.nav {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    align-items: center;
    width: 100%;
    padding: 15px 20px;
}

.nav-logo-section {
    display: flex;
    justify-content: center;
    align-items: center;
}

.nav-logo {
    width: 35px;
    height: 35px;
}

.nav-logo-name {
    margin-left: 10px;
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
}

.login-signup-section a {
    text-decoration: none;
    color: white;
    font-size: 0.9rem;
}

.nav-login {
    transition: all 0.5s ease;
}

.nav-login:hover {
    color: #2396b3;
}

.nav-register {
    border: 1px solid grey;
    border-radius: 8px;
    padding: 5px 18px;
    margin-left: 10px;
    transition: all 0.5s ease;
}

.nav-register:hover {
    background-color: #2396b3;
    border: 1px solid #2396b3;
}

.main-container {
    display: flex;
    /* grid-template-columns: 1fr 3fr; */
    padding: 0;
    height: calc(100vh - 70px - 2.5vh);
    width: 100vw;
    overflow: hidden;
}

.leftside-nav {
    width: 15vw;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
    margin-top: 6px;
    background-color: #1d2021;
    overflow-y: auto;
}

.leftside-nav-ul {
    padding: 0px;
}

.leftside-nav-ul a {
    text-decoration: none;
    color: #cfd1d0;
    font-size: 0.9rem;
    font-weight: 600;
}

.leftside-nav-ul li {
    padding: 7px;
}

.leftside-nav-ul li i {
    margin: 10px;
}

.pending-link {
    color: red;
}

.main-content {
    padding: 20px;
    /* overflow-y: auto; */
}

/* .rightside-nav {
    width: calc(100vw - 15vw);
    padding: 20px;
    background-color: #1d2021;
    overflow-y: auto;
} */

.main-section {
    width: calc(100vw - 15vw);
    /* height: auto;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    margin-top: 6px;
    padding: 18px; */
}

.page-info {
    width: 100%;
    text-align: left;
}

.page1 h5 {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0px;
    margin-bottom: 20px;
}

.task-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    margin: 20px;
}

.task-info h5 {
    margin: 0;
}

.upper-section {
    width: 90%;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
}

.greeting-user-section {
    margin-bottom: 20px;
}

.greeting-user {
    font-size: 2rem;
    font-weight: 600;
    margin: 0px;
}

.greeting-user-section div {
    margin: 0px;
    font-size: 2rem;
    font-weight: 600;
}

.add-task-button-section {
    display: flex;
    justify-content: center;
    align-items: center;
}

.add-task-button {
    width: 10vw;
    border: none;
    border-radius: 10px;
    background-color: #ff4155;
    color: white;
    font-size: 0.8rem;
    letter-spacing: 2px;
    padding: 10px 20px;
    transition: all 0.5s ease-in-out;
    /* text-transform: uppercase; */
    cursor: pointer;
}

.add-task-button:hover {
    transform: scale(1.1);
    background-color: #ff4155;
}

.btn-link {
    text-decoration: none;
    display: inline-block;
}

.category-section {
    height: 3vh;
    padding: 20px 40px;
    background-color: #1d2021;
    border-radius: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    box-shadow: 5px 5px 10px rgb(15, 15, 15);
    cursor: pointer;
}

.category-section img {
    width: 30px;
    height: 30px;
    margin-right: 10px;
}

.category-section h5 {
    font-size: 1rem;
}

.category-ul {
    display: flex;
    padding-left: 0;
    /* justify-content: center;
    align-items: center; */
    flex-direction: column;
}

.category-section-left {
    display: flex;
    align-items: center;
}

.category-section-right {
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 2px;
}

.work-category-list {
    width: 0;
    height: 0;
    margin: 10px;
    border-radius: 20px;
    background-color: #1d2021;
    box-shadow: 5px 5px 10px rgb(15, 15, 15);
    overflow: hidden;
    transition: all 0.8s ease-in-out;
    display: none;
}

li {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    border-radius: 10px;
    transition: all 0.5s ease-in-out;
    font-size: 0.8rem;
}

li:hover {
    background-color: #111213;
}

#work-category {
    transition: all 0.5s ease-in-out;
}

#personal-category {
    transition: all 0.5s ease-in-out;
}

#shopping-category {
    transition: all 0.5s ease-in-out;
}

#other-category {
    transition: all 0.5s ease-in-out;
}

#work-category:hover {
    background-color: #111213
}

#personal-category:hover {
    background-color: #111213
}

#shopping-category:hover {
    background-color: #111213
}

#other-category:hover {
    background-color: #111213
}

.personal-category-list {
    margin: 10px;
    border-radius: 20px;
    background-color: #1d2021;
    box-shadow: 5px 5px 10px rgb(15, 15, 15);
    overflow: hidden;
    display: none;
}

.shopping-category-list {
    margin: 10px;
    border-radius: 20px;
    background-color: #1d2021;
    box-shadow: 5px 5px 10px rgb(15, 15, 15);
    overflow: hidden;
    display: none;
}

.others-category-list {
    margin: 10px;
    border-radius: 20px;
    background-color: #1d2021;
    box-shadow: 5px 5px 10px rgb(15, 15, 15);
    overflow: hidden;
    display: none;
}

.work-category-list.show-work-category-list {
    width: 20vw;
    height: auto;
    display: block;
}

.personal-category-list.show-personal-category-list {
    display: block;
}

.shopping-category-list.show-shopping-category-list {
    display: block;
}

.others-category-list.show-others-category-list {
    display: block;
}

.list-main-section {
    width: 20vw;
    height: auto;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
}

.list-buttom-section {
    width: 20vw;
    height: 0vh;
    display: flex;
    justify-content: space-between;
    transition: all 0.3s ease-in-out;
    overflow: hidden;
}

.left-side-section.show-left-side-section.complete-task-section {
    border-radius: 0px 20px 0px 0px;
}

.list-buttom-section.show-list-buttom-section {
    height: 8vh;
    display: flex;
}

.inner-left-side {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-direction: column;
    justify-content: space-between;
}

.left-side-section {
    width: inherit;
    display: flex;
    justify-content: space-between;
    padding: 13px 30px;
    transition: all 0.5s ease-in-out;
}

.left-buttom-side-section {
    width: inherit;
    display: flex;
    justify-content: space-between;
    padding: 15px 30px;
}

.complete-task-section {
    width: 3vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #0aa06f;
}

.complete-task-section img {
    width: 30px;
    height: 30px;
}

.bottom-section {
    padding: 20px;
    overflow-y: auto;
}

.delete-task-section {
    width: 3vw;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #EB5927;
    border-radius: 0px 0px 20px 0px;
}

.delete-task-section img {
    width: 30px;
    height: 30px;
}

.bottom-section::-webkit-scrollbar {
    display: none;
}

.category-section h5 {
    margin: 0;
}

.display-task {
    font-size: 1.3rem;
    font-weight: 600;
}

.display-des {
    font-size: 0.9rem;
    font-weight: 400;
    margin: 5px 0px;
}

.right-side div {
    font-size: 1rem;
}

.display-currentdate {
    font-size: 0.8rem;
}

/* Close button styles are now in modal.css */

#add-task-form {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
}

.input-field {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
}

/* Add task section styles are now in modal.css */
#add-task-section.show {
    display: block;
}

/* Input styles are now in modal.css */

.input-task:focus {
    outline: none;
}

#description {
    width: 16vw;
    height: 10vh;
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    font-size: 0.9rem;
    /* font-family inherited from common.css */
    color: white;
    background-color: #181a1b;
    margin-top: 20px;
    resize: none;
    box-shadow: 5px 5px 10px rgb(14, 13, 13);
}

#description:focus {
    outline: none;
}

#description::-webkit-scrollbar {
    display: none;
}

.category-heading {
    width: 19vw;
}

.category-heading h5 {
    margin: 20px 20px 20px 10px;
}

.add-task-category-main-section {
    width: 18vw;
}

.add-task-category-section {
    display: grid;
    place-items: left;
    grid-template-columns: 0.1fr 0.1fr;
    grid-template-rows: 0.1fr 0.1fr;
    margin-bottom: 20px;
}

#work {
    width: 4vw;
    height: 6vh;
    background-color: #20212b;
    padding: 20px;
    margin: 10px;
    margin-left: 0;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    justify-content: space-between;
    box-shadow: 5px 5px 10px rgb(19, 19, 19);
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

#work.active-category {
    background-color: #1d1d23;
    transform: scale(1.1);
}

#work span {
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 1px;
}

#work img {
    width: 40px;
    height: 40px;
}

#personal {
    background-color: #20212b;
    padding: 20px;
    margin: 10px;
    margin-left: 0;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 5px 5px 10px rgb(19, 19, 19);
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

#personal.active-category {
    background-color: #1d1d23;
    transform: scale(1.1);
}

#personal span {
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 1px;
}

#personal img {
    width: 50px;
    height: 50px;
}

#shopping {
    width: 4vw;
    height: 6vh;
    background-color: #20212b;
    padding: 20px;
    margin: 10px;
    margin-left: 0;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    justify-content: space-between;
    box-shadow: 5px 5px 10px rgb(19, 19, 19);
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

#shopping.active-category {
    background-color: #1d1d23;
    transform: scale(1.1);
}

#shopping span {
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 1px;
}

#shopping img {
    width: 50px;
    height: 50px;
}

#others {
    width: 4vw;
    height: 6vh;
    background-color: #20212b;
    padding: 20px;
    margin: 10px;
    margin-left: 0;
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    justify-content: space-between;
    box-shadow: 5px 5px 10px rgb(19, 19, 19);
    cursor: pointer;
    transition: all 0.3s ease-in-out;
}

#others.active-category {
    background-color: #1d1d23;
    transform: scale(1.1);
}

#others span {
    font-size: 1rem;
    font-weight: 600;
    letter-spacing: 1px;
}

#others img {
    width: 50px;
    height: 50px;
}

.date-time {
    width: 18vw;
    display: flex;
    justify-content: flex-start;
    justify-content: space-between;
    gap: 1rem;
}

.input-date {
    width: 50%;
    height: 5vh;
    border: none;
    border-radius: 10px;
    padding: 0px 20px;
    font-size: 1rem;
    /* font-family inherited from common.css */
    color: white;
    background-color: #181a1b;
    text-transform: uppercase;
    box-shadow: 5px 5px 10px rgb(14, 13, 13);
}

.input-date:focus {
    outline: none;
}

.input-time {
    width: 50%;
    height: 5vh;
    border: none;
    border-radius: 10px;
    padding: 0px 20px;
    font-size: 1rem;
    /* font-family inherited from common.css */
    color: white;
    background-color: #181a1b;
    text-transform: uppercase;
    box-shadow: 5px 5px 10px rgb(14, 13, 13);
}

.input-time:focus {
    outline: none;
}

.add-new-task {
    width: 40%;
    border: none;
    border-radius: 10px;
    padding: 10px 20px;
    font-size: 0.9rem;
    /* font-family inherited from common.css */
    color: white;
    background-color: #ff4155;
    text-transform: uppercase;
    margin-top: 20px;
    transition: all 0.5s ease-in-out;
}

.add-new-task:hover {
    background-color: #181a1b;
    border: 1px solid #ff4155;
}

.aside-section {
    margin-top: 6px;
    background-color: #1d2021;
    padding: 20px;
    box-shadow: -10px 0 20px -15px black;

}

.profile-section {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 2rem 0;
    padding-bottom: 2rem;
    border-bottom: #181a1b 3px solid;
}

.username {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    letter-spacing: 1px;
}

.userimage {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 50%;
    border: 1px solid;
    width: 100px;
}

.colon {
    margin-left: 10px;
    font-size: 1.5rem;
    font-weight: 600;
}

.userimage img {
    width: 100%;
    height: 100%;
}

.profile-task-info-section {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding-bottom: 30px;
    /* justify-items: start; */
    /* padding-bottom: 2rem; */
    border-bottom: #181a1b 3px solid;
}


.profile-total-tasks-info,
.profile-completed-tasks-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.info-heading {
    margin: 0;
    font-size: 0.7rem;
    letter-spacing: 1px;
}

.info {
    margin: 5px 0px;
    font-size: 1.5rem;
}

.total-task-line {
    font-size: 1.5rem;
    color: #1d7b92;
}

.completed-task-line {
    font-size: 1.5rem;
    color: #32ba7c;
}

.pending-task-line {
    font-size: 1.5rem;
    color: red;
}

footer {
    width: 100%;
    height: 2.5vh;
    background-color: #121314;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
    display: flex;
    justify-content: center;
    align-items: center;
}

footer span {
    font-size: 0.7rem;
    font-weight: 600;
    color: #cfd1d0;
    letter-spacing: 1px;
    padding: 10px;
}

.task-filters {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    justify-content: flex-end;
}

.filter-btn {
    padding: 8px 16px;
    border: 1px solid #2a2e2f;
    border-radius: 6px;
    background-color: #242728;
    color: #cfd1d0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover {
    background-color: #1d7b92;
    border-color: #1d7b92;
}

.filter-btn.active {
    background-color: #1d7b92;
    border-color: #1d7b92;
}

.task-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.task-card {
    background-color: #1d2021;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.task-card:hover {
    transform: translateY(-2px);
}

.task-details {
    padding: 15px;
}

.task-title {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 8px;
    color: #fff;
}

.task-description {
    font-size: 0.9rem;
    color: #cfd1d0;
    margin-bottom: 12px;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 12px;
}

.task-actions {
    display: flex;
    gap: 10px;
}

.status-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.status-btn.pending {
    background-color: #ffc107;
    color: #000;
}

.status-btn.completed {
    background-color: #28a745;
    color: #fff;
}

.delete-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    background-color: #dc3545;
    color: #fff;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s ease;
}

.delete-btn:hover {
    background-color: #c82333;
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 24px;
    border-radius: 4px;
    color: #fff;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.notification.success {
    background-color: #28a745;
}

.notification.error {
    background-color: #dc3545;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@media screen and (max-width: 768px) {
    .task-filters {
        flex-wrap: wrap;
        justify-content: center;
    }

    .filter-btn {
        width: calc(33.33% - 10px);
        text-align: center;
    }

    .task-container {
        grid-template-columns: 1fr;
    }

    .task-card {
        margin: 0 10px;
    }

    .add-task-button{
        width: 20vw;
    }
}

/* Tablet Layout (601px to 1024px) */
@media screen and (min-width: 601px) and (max-width: 1024px) {
    /* .main-container {
        grid-template-columns: 250px 1fr;
        margin-top: 6px;
    } */

    .leftside-nav {
        width: 30vw;
    }

    .main-section { 
        width: calc(100vw - 30vw)!impor;
    }


    .rightside-nav,
    .aside-section {
        display: none;
    }

    .nav {
        padding: 10px 20px;
    }

    .nav-logo-name {
        font-size: 1.2rem;
    }

    .main-content {
        padding: 15px;
    }

    .task-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .task-card {
        width: 100%;
    }

    .greeting-user-section {
        margin-bottom: 20px;
    }

    .greeting-user-section h2 {
        font-size: 1.2rem;
    }

    .add-task-button {
        font-size: 0.9rem;
        width: 20vw;
        letter-spacing: 1px;
    }

    .category-section {
        width: 100%;
        margin-bottom: 15px;
    }

    .task-details {
        padding: 15px;
    }
}

/* Mobile Layout (up to 600px) */
@media screen and (max-width: 600px) {
    

    .nav {
        flex-direction: column;
        padding: 10px;
        gap: 10px;
    }

    .nav-logo-section {
        width: 100%;
        justify-content: center;
    }

    .login-signup-section {
        width: 100%;
        display: flex;
        justify-content: center;
        gap: 15px;
    }

    .main-container {
        grid-template-columns: 1fr;
        margin-top: 5px;
        overflow-y: auto;
    }

    .leftside-nav {
        position: fixed;
        left: -100%;
        top: 100px;
        width: 250px;
        height: calc(100vh - 100px);
        transition: 0.3s;
        z-index: 900;
    }

    .leftside-nav.active {
        left: 0;
    }

    .main-content {
        padding: 10px;
        margin-left: 0;
    }

    .task-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .task-card {
        width: 100%;
    }

    .greeting-user-section {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 15px;
        margin-bottom: 20px;
    }

    .greeting-user-section h2 {
        font-size: 1.1rem;
    }

    .add-task-button {
        width: 100%;
        padding: 10px;
        font-size: 0.9rem;
    }

    .category-section {
        width: 100%;
        margin-bottom: 15px;
    }

    .task-details {
        padding: 12px;
    }

    .task-title {
        font-size: 1rem;
    }

    .task-description {
        font-size: 0.9rem;
    }

    .task-date,
    .task-time {
        font-size: 0.8rem;
    }

    /* Form adjustments for mobile */
    .add-task-form {
        width: 90%;
        padding: 15px;
    }

    .add-task-category-section {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .form-group {
        margin-bottom: 15px;
    }

    .form-group input,
    .form-group textarea {
        padding: 8px;
        font-size: 0.9rem;
    }

    footer {
        height: 5vh;
    }

}

@media (max-width: 480px) {

}

