/* Navigation Bar Improvements */

/* Container for the navigation */
.nav-container {
    width: 100%;
    background-color: #1d2021;
    display: flex;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 15px 20px;
}

/* Logo container */
.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 15vw;
}

/* Logo image */
.logo img {
    width: 32px;
    height: 32px;
    transition: transform 0.3s ease;
}

.logo:hover img {
    transform: scale(1.1);
}

/* Navigation links */
.nav-links {
    display: flex;
    align-items: center;
}

.nav-links ul {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-links li {
    margin: 0 10px;
}

.nav-links a {
    color: #fff;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.nav-links a:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Active link */
.nav-links a.active {
    background-color: #2396b3;
    color: white;
}

/* Logout link */
.nav-links a[href="/logout"] {
    border: 1px solid #2396b3;
    padding: 6px 15px;
    border-radius: 4px;
}

.nav-links a[href="/logout"]:hover {
    background-color: #2396b3;
}

.nav-logo-section {
    display: flex;
    justify-content: center;
    align-items: center;
}

.nav-logo {
    width: 35px;
    height: 35px;
}

.nav-logo-name {
    margin-left: 10px;
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
}

.login-signup-section a {
    text-decoration: none;
    color: white;
    font-size: 0.9rem;
}

.nav-login {
    transition: all 0.5s ease;
}

.nav-login:hover {
    color: #2396b3;
}

.nav-register {
    border: 1px solid grey;
    border-radius: 8px;
    padding: 5px 18px;
    margin-left: 10px;
    transition: all 0.5s ease;
}

.nav-register:hover {
    background-color: #2396b3;
    border: 1px solid #2396b3;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #fff;
    font-size: 24px;
    cursor: pointer;
}

/* Responsive styles */
@media (max-width: 768px) {
    .nav {
        padding: 10px;
    }
    
    .logo img {
        width: 28px;
        height: 28px;
    }
    
    .nav-links ul {
        gap: 5px;
    }
    
    .nav-links a {
        padding: 6px 10px;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .nav {
        flex-direction: row;
        gap: 10px;
    }
    
    .nav-links {
        display: none;
    }

    .menu-toggle {
        display: block;
    }
    
    .nav-links ul {
        width: 100%;
        justify-content: space-around;
    }
    
    .nav-links li {
        margin: 0 5px;
    }
}
