<!-- Optimized Dashboard Content -->
<%
// Pre-process data for better performance
const tasksByCategory = {};
const categoryStats = {};
let totalTasks = 0;
let completedTasks = 0;

// Default categories with their metadata
const defaultCategories = [
  {
    id: 'work',
    name: 'Work',
    icon: 'https://cdn-icons-png.flaticon.com/512/3281/3281289.png',
    color: '#4CAF50'
  },
  {
    id: 'personal',
    name: 'Personal',
    icon: 'https://cdn-icons-png.flaticon.com/512/1077/1077012.png',
    color: '#2196F3'
  },
  {
    id: 'shopping',
    name: 'Shopping',
    icon: 'https://cdn-icons-png.flaticon.com/512/4290/4290854.png',
    color: '#FF9800'
  },
  {
    id: 'others',
    name: 'Others',
    icon: 'https://cdn-icons-png.flaticon.com/512/7245/7245102.png',
    color: '#9C27B0'
  }
];

// Initialize category data
defaultCategories.forEach(cat => {
  tasksByCategory[cat.id] = [];
  categoryStats[cat.id] = { total: 0, completed: 0 };
});

// Process tasks once
if (tasks && tasks.length > 0) {
  tasks.forEach(task => {
    totalTasks++;
    if (task.completed) completedTasks++;

    const category = task.categoryChoosed || 'others';
    if (!tasksByCategory[category]) {
      tasksByCategory[category] = [];
      categoryStats[category] = { total: 0, completed: 0 };
    }

    tasksByCategory[category].push(task);
    categoryStats[category].total++;
    if (task.completed) categoryStats[category].completed++;
  });
}

const pendingTasks = totalTasks - completedTasks;
%>

<div class="dashboard-header">
  <div class="greeting-section">
    <h2 class="page-title"><%= title %></h2>
    <p class="page-description">
      <% if (activeTab === 'pending') { %>
        Here are all your pending tasks that need to be completed
      <% } else if (activeTab === 'completed') { %>
        Great job! Here are all the tasks you've completed
      <% } else { %>
        Here are all your tasks organized by category
      <% } %>
    </p>
  </div>

  <div class="action-section">
    <div class="task-summary">
      <span class="summary-item">
        <strong><%= totalTasks %></strong> Total
      </span>
      <span class="summary-item">
        <strong><%= pendingTasks %></strong> Pending
      </span>
      <span class="summary-item">
        <strong><%= completedTasks %></strong> Completed
      </span>
    </div>
    <a href="/tasks/manage" class="btn btn-primary">
      <i class="fas fa-tasks"></i> Manage Tasks
    </a>
  </div>
</div>

<div class="tasks-container" id="tasks-container">
  <% if (totalTasks === 0) { %>
    <div class="empty-state">
      <div class="empty-icon">
        <i class="fas fa-clipboard-list"></i>
      </div>
      <h3>No tasks yet</h3>
      <p>Start by creating your first task to get organized!</p>
      <a href="/tasks/manage" class="btn btn-primary">
        <i class="fas fa-plus"></i> Create Task
      </a>
    </div>
  <% } else { %>
    <% defaultCategories.forEach(category => { %>
      <% const categoryTasks = tasksByCategory[category.id] || []; %>
      <% const stats = categoryStats[category.id] || { total: 0, completed: 0 }; %>

      <% if (stats.total > 0) { %>
        <div class="category-section" data-category="<%= category.id %>">
          <div class="category-header">
            <div class="category-info">
              <img src="<%= category.icon %>" alt="<%= category.name %>" class="category-icon" loading="lazy">
              <div class="category-details">
                <h3 class="category-name"><%= category.name %></h3>
                <span class="category-count">
                  <%= stats.total %> task<%= stats.total !== 1 ? 's' : '' %>
                  <% if (stats.completed > 0) { %>
                    • <%= stats.completed %> completed
                  <% } %>
                </span>
              </div>
            </div>
            <div class="category-progress">
              <% const progressPercent = stats.total > 0 ? Math.round((stats.completed / stats.total) * 100) : 0; %>
              <div class="progress-bar">
                <div class="progress-fill" style="width: <%= progressPercent %>%; background-color: <%= category.color %>;"></div>
              </div>
              <span class="progress-text"><%= progressPercent %>%</span>
            </div>
          </div>

          <div class="tasks-list">
            <% categoryTasks.slice(0, 5).forEach(task => { %>
              <div class="task-item <%= task.completed ? 'completed' : '' %>" data-task-id="<%= task._id %>">
                <div class="task-content">
                  <div class="task-main">
                    <h4 class="task-title"><%= task.title %></h4>
                    <% if (task.description) { %>
                      <p class="task-description"><%= task.description.substring(0, 100) %><%= task.description.length > 100 ? '...' : '' %></p>
                    <% } %>
                  </div>
                  <div class="task-meta">
                    <span class="task-date">
                      <i class="fas fa-calendar"></i>
                      <%= task.date %>
                      <% if (task.time) { %>
                        <span class="task-time"><%= task.time %></span>
                      <% } %>
                    </span>
                    <% if (task.currentDate) { %>
                      <span class="task-created">
                        Created: <%= task.currentDate %>
                      </span>
                    <% } %>
                  </div>
                </div>

                <div class="task-actions">
                  <% if (task.completed) { %>
                    <form action="/tasks/<%= task._id %>/status?_method=PATCH" method="POST" class="inline-form">
                      <button type="submit" class="btn-icon btn-undo" title="Mark as pending">
                        <i class="fas fa-undo"></i>
                      </button>
                    </form>
                  <% } else { %>
                    <form action="/tasks/<%= task._id %>/status?_method=PATCH" method="POST" class="inline-form">
                      <button type="submit" class="btn-icon btn-complete" title="Mark as completed">
                        <i class="fas fa-check"></i>
                      </button>
                    </form>
                  <% } %>
                  <a href="/tasks/<%= task._id %>" class="btn-icon btn-view" title="View details">
                    <i class="fas fa-eye"></i>
                  </a>
                  <a href="/tasks/delete/<%= task._id %>" class="btn-icon btn-delete" title="Delete task" onclick="return confirm('Are you sure you want to delete this task?')">
                    <i class="fas fa-trash"></i>
                  </a>
                </div>
              </div>
            <% }); %>

            <% if (categoryTasks.length > 5) { %>
              <div class="show-more">
                <button class="btn-link show-more-btn" data-category="<%= category.id %>">
                  Show <%= categoryTasks.length - 5 %> more tasks
                  <i class="fas fa-chevron-down"></i>
                </button>
              </div>
            <% } %>
          </div>
        </div>
      <% } %>
    <% }); %>
  <% } %>
</div>

<style>
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 2rem;
}

.greeting-section {
  flex: 1;
}

.page-title {
  margin: 0 0 0.5rem 0;
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.task-summary {
  display: flex;
  gap: 1rem;
}

.summary-item {
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #666;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.category-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 2rem;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.category-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0,0,0,0.15);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.category-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.category-name {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
}

.category-count {
  font-size: 0.9rem;
  color: #666;
}

.category-progress {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.progress-bar {
  width: 100px;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.85rem;
  font-weight: 600;
  color: #666;
  min-width: 35px;
}

.tasks-list {
  padding: 1rem;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1rem;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  transition: all 0.2s ease;
}

.task-item:hover {
  border-color: #007bff;
  background: #f8f9ff;
}

.task-item.completed {
  opacity: 0.7;
  background: #f8f9fa;
}

.task-item.completed .task-title {
  text-decoration: line-through;
}

.task-content {
  flex: 1;
  min-width: 0;
}

.task-title {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  word-wrap: break-word;
}

.task-description {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: #666;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.85rem;
  color: #888;
}

.task-date {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.task-time {
  margin-left: 0.5rem;
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: 0.5rem;
  margin-left: 1rem;
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f8f9fa;
  color: #666;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.btn-icon:hover {
  transform: translateY(-1px);
}

.btn-complete:hover {
  background: #28a745;
  color: white;
}

.btn-undo:hover {
  background: #ffc107;
  color: white;
}

.btn-view:hover {
  background: #17a2b8;
  color: white;
}

.btn-delete:hover {
  background: #dc3545;
  color: white;
}

.inline-form {
  display: inline;
}

.show-more {
  text-align: center;
  padding: 1rem 0;
  border-top: 1px solid #e9ecef;
  margin-top: 0.75rem;
}

.btn-link {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-link:hover {
  color: #0056b3;
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: stretch;
  }

  .action-section {
    flex-direction: column;
    align-items: stretch;
  }

  .task-summary {
    justify-content: space-between;
  }

  .task-item {
    flex-direction: column;
    gap: 1rem;
  }

  .task-actions {
    margin-left: 0;
    justify-content: flex-end;
  }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  // Handle show more functionality
  document.querySelectorAll('.show-more-btn').forEach(btn => {
    btn.addEventListener('click', function() {
      const category = this.dataset.category;
      // This would typically load more tasks via AJAX
      console.log('Load more tasks for category:', category);
    });
  });

  // Add loading states for form submissions
  document.querySelectorAll('.inline-form').forEach(form => {
    form.addEventListener('submit', function() {
      const btn = this.querySelector('button');
      btn.disabled = true;
      btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    });
  });
});
</script>
