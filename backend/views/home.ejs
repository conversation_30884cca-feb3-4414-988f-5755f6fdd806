<html>

<head>
    <title>
        <%= title %>
    </title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/css/home.css">
    <link rel="icon" href="https://cdn-icons-png.flaticon.com/512/7692/7692809.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
</head>

<body>

    <div class="nav-container">
        <div class="nav">
            <div class="nav-logo-section">
                <a href=""><img src="https://cdn-icons-png.flaticon.com/512/9802/9802618.png" alt="" class="nav-logo"></a>
                <a href=""><span class="nav-logo-name">Todos.</span></a>
            </div>
            <div class="login-signup-section">
                <a href="/login" class="nav-login">Login</a>
                <a href="/register" class="nav-register">Sign Up</a>
            </div>
            <span class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </span>
        </div>
        
    </div>
    <div class="main-container">
        <h4 class="main-heading">Todos, just tasks</h4>
        <div class="about">
            <p>keep track of the daily tasks in life and get that satisfaction upon completion.</p>
        </div>
        <div class="buttons">
            <a href="/task"><button class="get-start" id="start">Get Started</button></a>
            <button class="learn-more" id="more">Learn More</button>
        </div>
    </div>
    <div class="learn-more-container" id="learn-more-container">
        <img src="https://cdn-icons-png.flaticon.com/512/2976/2976286.png" alt="" class="close-button">
        <p class="para1">
            <span class="para-first-word">To-do lists</span> are a list of tasks that an individual needs to complete or accomplish.
            Tasks are typically put in order by priority or importance. A to-do list can be written
            on a piece of paper or by utilizing task management software. Items on the list can
            pertain to one's personal or professional life.
        </p>
        <p class="para-heading">
            Benefits of to-do lists
        </p>
        <p class="para2">
            When done correctly, there are many benefits one may experience when creating a to-do list. Some of these
            benefits are:

            Sharpen memory: A to-do list can act as an external memory aid since it’s only possible to hold a few pieces
            of information at once. Keeping a to-do list up-to-date makes it easy to keep track of every task. This list
            also can reinforce the information in case something is forgotten.
            Increase productivity: Making a to-do list can prioritize the most important tasks and need to be completed
            first. This prevents wasting time on tasks that don’t require immediate attention.
            Boost motivation: A to-do list can be used as a motivational tool because it helps to clarify and break down
            long-term goals into smaller, more achievable short-term goals. Additionally, as someone crosses items off a
            to-do list, they’re likely to experience a boost in confidence, too.
            Improve time management: Since tasks on a to-do list should be organized by importance, understanding which
            projects or items need to be worked on first can improve time management.
        </p>
    </div>

    <!-- background element -->
    <!-- <div class="element1"></div>
    <div class="element2"></div>
    <div class="element3"></div> -->

    <script src="/js/home.js"></script>
    <script>

        
        

        // Toggle menu on click
        document.getElementById('menuToggle').addEventListener('click', function() {
            document.querySelector('.nav').classList.toggle('active');
        });

        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            const nav = document.querySelector('.nav');
            const menuToggle = document.getElementById('menuToggle');
            if (!nav.contains(e.target) && e.target !== menuToggle) {
                nav.classList.remove('active');
            }
        });
    </script>
</body>

</html>