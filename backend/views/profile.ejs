
    <style>
        
        /* Profile form styles */
        .profile-form-container {
            background-color: #1d2021;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .profile-form-title {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #cfd1d0;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }

        .profile-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 5px;
            color: #cfd1d0;
            font-size: 0.9rem;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            background-color: #121314;
            border: 1px solid #333;
            border-radius: 4px;
            color: #cfd1d0;
            font-size: 0.9rem;
        }

        .form-control:focus {
            outline: none;
            border-color: #4CAF50;
        }

        .form-select {
            width: 100%;
            padding: 8px 12px;
            background-color: #121314;
            border: 1px solid #333;
            border-radius: 4px;
            color: #cfd1d0;
            font-size: 0.9rem;
        }

        .profile-image-container {
            text-align: center;
            margin-bottom: 20px;
        }

        .profile-image {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #4CAF50;
        }

        .image-upload-container {
            margin-top: 10px;
        }

        .btn-file {
            position: relative;
            overflow: hidden;
            background-color: #4CAF50;
            color: white;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            display: inline-block;
            font-size: 0.9rem;
        }

        .btn-file input[type=file] {
            position: absolute;
            top: 0;
            right: 0;
            min-width: 100%;
            min-height: 100%;
            font-size: 100px;
            text-align: right;
            filter: alpha(opacity=0);
            opacity: 0;
            outline: none;
            background: white;
            cursor: pointer;
            display: block;
        }

        .btn-update {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
            margin-top: 15px;
        }

        .btn-update:hover {
            background-color: #45a049;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .profile-section-title {
            font-size: 1.2rem;
            margin: 20px 0 10px;
            color: #cfd1d0;
            border-bottom: 1px solid #333;
            padding-bottom: 5px;
        }

        .alert {
            padding: 10px 15px;
            border-radius: 4px;
            margin-bottom: 15px;
            color: white;
        }

        .alert-success {
            background-color: #4CAF50;
        }

        .alert-danger {
            background-color: #F44336;
        }
    </style>


        
    <div class="main-section-container">
        <div class="main-section-content">
            <div class="profile-form-container">
                <h2 class="profile-form-title">My Profile</h2>

                

                <form action="/profile" method="POST" enctype="multipart/form-data" class="profile-form">
                    <div class="full-width profile-image-container">
                        <img src="<%= user.profileImage || '/img/avatar.svg' %>" alt="Profile Image" class="profile-image" id="profile-preview">
                        <div class="image-upload-container">
                            <label class="btn-file">
                                <i class="fas fa-camera"></i> Change Photo
                                <input type="file" name="profileImage" id="profile-image-input" accept="image/*">
                            </label>
                        </div>
                    </div>

                    <h3 class="full-width profile-section-title">Basic Information</h3>

                    <div class="form-group">
                        <label for="firstName" class="form-label">First Name</label>
                        <input type="text" id="firstName" name="firstName" class="form-control" value="<%= user.firstName %>" required>
                    </div>

                    <div class="form-group">
                        <label for="lastName" class="form-label">Last Name</label>
                        <input type="text" id="lastName" name="lastName" class="form-control" value="<%= user.lastName %>" required>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" id="email" name="email" class="form-control" value="<%= user.email %>" required>
                    </div>

                    <div class="form-group">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="tel" id="phone" name="phone" class="form-control" value="<%= user.phone %>" required pattern="^\+?[0-9]{7,15}$">
                        <small style="color: #999; font-size: 0.8rem;">Format: +1234567890 or 1234567890 (7-15 digits)</small>
                    </div>

                    <h3 class="full-width profile-section-title">Personal Details</h3>

                    <div class="form-group">
                        <label for="gender" class="form-label">Gender</label>
                        <select id="gender" name="gender" class="form-select" required>
                            <option value="Male" <%= user.gender === 'Male' ? 'selected' : '' %>>Male</option>
                            <option value="Female" <%= user.gender === 'Female' ? 'selected' : '' %>>Female</option>
                            <option value="Other" <%= user.gender === 'Other' ? 'selected' : '' %>>Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="dob" class="form-label">Date of Birth</label>
                        <input type="date" id="dob" name="dob" class="form-control" value="<%= user.dob %>" required>
                    </div>

                    <h3 class="full-width profile-section-title">Address Information</h3>

                    <div class="form-group">
                        <label for="address" class="form-label">Address</label>
                        <input type="text" id="address" name="address" class="form-control" value="<%= user.address || '' %>">
                    </div>

                    <div class="form-group">
                        <label for="city" class="form-label">City</label>
                        <input type="text" id="city" name="city" class="form-control" value="<%= user.city || '' %>">
                    </div>

                    <div class="form-group">
                        <label for="zipcode" class="form-label">Zip Code</label>
                        <input type="text" id="zipcode" name="zipcode" class="form-control" value="<%= user.zipcode || '' %>" pattern="^\d{5}(-\d{4})?$">
                    </div>

                    <h3 class="full-width profile-section-title">Security</h3>

                    <div class="form-group full-width">
                        <label for="password" class="form-label">New Password (leave blank to keep current)</label>
                        <input type="password" id="password" name="password" class="form-control">
                    </div>

                    <div class="form-group full-width">
                        <button type="submit" class="btn-update">
                            <i class="fas fa-save"></i> Update Profile
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
            


    <script>
        // Flash message auto-dismiss
        setTimeout(function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                message.style.opacity = '0';
                message.style.transition = 'opacity 0.5s';
                setTimeout(function() {
                    message.remove();
                }, 500);
            });
        }, 5000);

        // Preview the profile image before upload
        document.getElementById('profile-image-input').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    document.getElementById('profile-preview').src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
