    <div class="upper-section">
        <div class="greeting-user-section">
        <h6 class="greeting-user">--------</h6>
        </div>
    </div>

    <!-- Flash Messages Container -->
    <div class="flash-messages"></div>

    <div class="task-management-header">
        <h1> Task Management</h1>
        <div class="task-actions">
            <button id="new-task-btn" class="btn btn-primary"><i class="fas fa-plus"></i> Create New Task</button>
        </div>
    </div>

    <div class="task-filters">
        <div class="filter-group">
            <label for="category-filter"><i class="fas fa-filter"></i> Filter by Category:</label>
            <select id="category-filter">
                <option value="all">All Categories</option>
                <!-- Categories will be loaded dynamically -->
            </select>
        </div>
        <div class="filter-group">
            <label for="status-filter"><i class="fas fa-tasks"></i> Filter by Status:</label>
            <select id="status-filter">
                <option value="all">All Statuses</option>
                <option value="pending">Pending</option>
                <option value="completed">Completed</option>
            </select>
        </div>
        <div class="filter-group">
            <button id="apply-filters" class="btn btn-secondary"><i class="fas fa-search"></i> Apply Filters</button>
            <button id="reset-filters" class="btn btn-outline"><i class="fas fa-undo"></i> Reset</button>
        </div>
    </div>

    <!-- Category summary container removed as requested -->

    <div class="task-list-container">
        <table class="task-table">
            <thead>
                <tr>
                    <th><i class="fas fa-file-alt"></i> Title</th>
                    <th><i class="fas fa-tag"></i> Category</th>
                    <th><i class="fas fa-calendar-alt"></i> Due Date</th>
                    <th><i class="fas fa-info-circle"></i> Status</th>
                    <th><i class="fas fa-cogs"></i> Actions</th>
                </tr>
            </thead>
            <tbody id="task-list">
                <!-- Tasks will be loaded dynamically -->
                <tr class="loading-row">
                    <td colspan="5"><i class="fas fa-spinner fa-spin"></i> Loading tasks...</td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Task Form Modal -->
    <div id="task-form-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="task-form-title"><i class="fas fa-edit"></i> Create New Task</h3>
                <span class="close-modal">&times;</span>
            </div>
            <form id="task-form">
                <!-- Add hidden input fields for category data -->
                <input type="hidden" id="task-id" name="taskId">
                <input type="hidden" id="category-id" name="category">
                <input type="hidden" id="category-choosed" name="categoryChoosedValue">

                <div class="input-field">
                    <label for="task-title"><i class="fas fa-heading"></i> Task Title</label>
                    <input type="text" id="task-title" name="title" placeholder="Enter task title" required>
                </div>

                <div class="input-field">
                    <label for="task-description"><i class="fas fa-align-left"></i> Description</label>
                    <textarea id="task-description" name="description" placeholder="Enter task description"></textarea>
                </div>

                <!-- Category dropdown removed as requested -->

                <div class="input-field">
                    <label><i class="fas fa-palette"></i> Category Icons</label>
                    <div id="categories-container" class="categories-container">
                        <!-- Categories will be loaded dynamically -->
                        <div class="loading-categories">Loading categories...</div>
                    </div>
                </div>

                <div class="input-field">
                    <label for="task-date"><i class="fas fa-calendar-alt"></i> Due Date</label>
                    <input type="date" id="task-date" name="date" required>
                </div>

                <div class="input-field">
                    <label for="task-time"><i class="fas fa-clock"></i> Due Time (optional)</label>
                    <input type="time" id="task-time" name="time">
                </div>

                <div class="form-actions">
                    <button type="button" id="cancel-task-btn" class="btn btn-outline"><i class="fas fa-times"></i> Cancel</button>
                    <button type="submit" id="save-task-btn" class="btn btn-primary"><i class="fas fa-save"></i> Save Task</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="delete-confirm-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-trash-alt"></i> Confirm Delete</h3>
                <span class="close-delete-modal">&times;</span>
            </div>
            <div class="delete-modal-content">
                <p>Are you sure you want to delete this task? This action cannot be undone.</p>
                <div class="delete-modal-actions">
                    <button id="cancel-delete-btn" class="btn btn-outline"><i class="fas fa-times"></i> Cancel</button>
                    <button id="confirm-delete-btn" class="btn btn-danger"><i class="fas fa-trash-alt"></i> Delete</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Template -->
    <div id="notification-template" class="notification" style="display: none;"></div>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
    <script src="/js/dashboard.js"></script>

    <style>
        /* Category Summary Styles removed as requested */

        .category-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: capitalize;
        }

        .categories-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .category {
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .category:hover {
            transform: translateY(-3px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .category.selected, .category.active-category {
            border-color: #4CAF50;
            background-color: rgba(76, 175, 80, 0.1);
        }

        .category img {
            width: 30px;
            height: 30px;
            margin-bottom: 5px;
        }

        /* Flash Messages */
        .flash-messages {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .flash-message {
            padding: 12px 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            min-width: 250px;
        }

        .flash-success {
            background-color: #4CAF50;
            color: white;
        }

        .flash-error {
            background-color: #f44336;
            color: white;
        }

        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
    </style>

    <script>
        // Task management functionality
        document.addEventListener('DOMContentLoaded', function() {
            // DOM elements
            const taskFormModal = document.getElementById('task-form-modal');
            const deleteConfirmModal = document.getElementById('delete-confirm-modal');
            const newTaskBtn = document.getElementById('new-task-btn');
            const closeModalBtn = document.querySelector('.close-modal');
            const closeDeleteModalBtn = document.querySelector('.close-delete-modal');
            const taskForm = document.getElementById('task-form');
            const cancelTaskBtn = document.getElementById('cancel-task-btn');
            const confirmDeleteBtn = document.getElementById('confirm-delete-btn');
            const cancelDeleteBtn = document.getElementById('cancel-delete-btn');
            const categoryFilter = document.getElementById('category-filter');
            const statusFilter = document.getElementById('status-filter');
            const applyFiltersBtn = document.getElementById('apply-filters');
            const resetFiltersBtn = document.getElementById('reset-filters');

            let currentTaskId = null;
            let tasks = [];
            let categories = [];

            // Initialize
            loadTasks();
            loadCategories();

            // Event listeners
            newTaskBtn.addEventListener('click', () => openTaskForm());
            closeModalBtn.addEventListener('click', () => closeTaskForm());
            cancelTaskBtn.addEventListener('click', () => closeTaskForm());
            closeDeleteModalBtn.addEventListener('click', () => closeDeleteConfirm());
            cancelDeleteBtn.addEventListener('click', () => closeDeleteConfirm());
            confirmDeleteBtn.addEventListener('click', deleteTask);
            taskForm.addEventListener('submit', saveTask);
            applyFiltersBtn.addEventListener('click', applyFilters);
            resetFiltersBtn.addEventListener('click', resetFilters);

            // Close modals when clicking outside
            window.addEventListener('click', function(event) {
                if (event.target === taskFormModal) {
                    closeTaskForm();
                }
                if (event.target === deleteConfirmModal) {
                    closeDeleteConfirm();
                }
            });

            // Functions
            function openTaskForm(taskId = null) {
                // Reset form
                taskForm.reset();
                document.getElementById('category-choosed').value = '';
                document.getElementById('category-id').value = '';

                // Reset category selections in the categories container
                document.querySelectorAll('.category').forEach(cat => {
                    cat.classList.remove('selected');
                    cat.classList.remove('active-category');
                });

                // Set default date to today
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('task-date').value = today;
                document.getElementById('task-time').value = '';

                // If editing existing task
                if (taskId) {
                    currentTaskId = taskId;
                    const task = tasks.find(t => t._id === taskId);
                    if (task) {
                        document.getElementById('task-form-title').textContent = 'Edit Task';
                        document.getElementById('task-id').value = task._id;
                        document.getElementById('task-title').value = task.title;
                        document.getElementById('task-description').value = task.description || '';
                        document.getElementById('task-date').value = task.date;
                        document.getElementById('task-time').value = task.time || '';
                        document.getElementById('category-choosed').value = task.categoryChoosed;
                        document.getElementById('category-id').value = task.category || '';

                        // Category dropdown selection removed

                        // Select the category in the categories container
                        const categoryEl = document.querySelector(`.category[data-id="${task.categoryChoosed}"]`);
                        if (categoryEl) {
                            categoryEl.classList.add('selected');
                            categoryEl.classList.add('active-category');
                        }
                    }
                } else {
                    document.getElementById('task-form-title').textContent = 'Create New Task';
                    currentTaskId = null;
                }

                taskFormModal.style.display = 'block';
            }

            function closeTaskForm() {
                taskFormModal.style.display = 'none';
            }

            function openDeleteConfirm(taskId) {
                currentTaskId = taskId;
                deleteConfirmModal.style.display = 'block';
            }

            function closeDeleteConfirm() {
                deleteConfirmModal.style.display = 'none';
                currentTaskId = null;
            }

            async function loadTasks(categoryFilter = 'all', statusFilter = 'all') {
                const taskList = document.getElementById('task-list');
                taskList.innerHTML = '<tr class="loading-row"><td colspan="5">Loading tasks...</td></tr>';

                try {
                    let url = '/api/tasks';
                    if (statusFilter !== 'all') {
                        url = `/api/tasks/${statusFilter}`;
                    }
                    console.log("Url: " +url)
                    const response = await fetch(url);
                    if (!response.ok) {
                        throw new Error('Failed to fetch tasks');
                    }

                    tasks = await response.json();

                    console.log("Tasks: " , tasks)

                    // Filter by category if needed
                    if (categoryFilter !== 'all') {
                        tasks = tasks.filter(task => task.categoryChoosed === categoryFilter);
                    }

                    // Render tasks
                    renderTasks(tasks);
                } catch (error) {
                    console.error('Error loading tasks:', error);
                    taskList.innerHTML = '<tr><td colspan="5">Error loading tasks. Please try again.</td></tr>';
                }
            }

            function renderTasks(tasks) {
                const taskList = document.getElementById('task-list');

                if (tasks.length === 0) {
                    taskList.innerHTML = '<tr><td colspan="5">No tasks found</td></tr>';
                    return;
                }

                taskList.innerHTML = '';

                tasks.forEach(task => {
                    const row = document.createElement('tr');
                    row.className = task.completed ? 'completed-task' : '';

                    row.innerHTML = `
                        <td>${task.title}</td>
                        <td><span class="category-badge ${task.categoryChoosed}">${task.categoryChoosed}</span></td>
                        <td>${task.date}${task.time ? ' ' + task.time : ''}</td>
                        <td>
                            <span class="status-badge ${task.completed ? 'completed' : 'pending'}">
                                ${task.completed ? 'Completed' : 'Pending'}
                            </span>
                        </td>
                        <td class="actions">
                            <button class="btn-icon toggle-status" data-id="${task._id}" data-status="${task.completed}">
                                <i class="fas ${task.completed ? 'fa-times' : 'fa-check'}"></i>
                            </button>
                            <button class="btn-icon edit-task" data-id="${task._id}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon delete-task" data-id="${task._id}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `;

                    taskList.appendChild(row);
                });

                // Add event listeners to action buttons
                document.querySelectorAll('.toggle-status').forEach(btn => {
                    btn.addEventListener('click', () => toggleTaskStatus(btn.dataset.id, btn.dataset.status === 'true'));
                });

                document.querySelectorAll('.edit-task').forEach(btn => {
                    btn.addEventListener('click', () => openTaskForm(btn.dataset.id));
                });

                document.querySelectorAll('.delete-task').forEach(btn => {
                    btn.addEventListener('click', () => openDeleteConfirm(btn.dataset.id));
                });
            }

            async function loadCategories() {
                try {
                    const response = await fetch('/categories/api');
                    if (!response.ok) {
                        throw new Error('Failed to fetch categories');
                    }

                    const data = await response.json();
                    categories = data.categories;

                    console.log("Categories: " , categories)

                    // Populate category filter
                    const categoryFilter = document.getElementById('category-filter');
                    categoryFilter.innerHTML = '<option value="all">All Categories</option>';

                    categories.forEach(category => {
                        const option = document.createElement('option');
                        option.value = category.name.toLowerCase();
                        option.textContent = category.name;
                        categoryFilter.appendChild(option);
                    });

                    // Populate categories in task form
                    const categoriesContainer = document.getElementById('categories-container');

                    if (categories.length > 0) {
                        categoriesContainer.innerHTML = '';

                        categories.forEach(category => {
                            const categoryDiv = document.createElement('div');
                            categoryDiv.className = 'category';
                            categoryDiv.id = 'personal';
                            categoryDiv.dataset.id = category.name.toLowerCase();
                            categoryDiv.dataset.categoryId = category._id;

                            if (category.color) {
                                categoryDiv.style.borderColor = category.color;
                            }

                            categoryDiv.innerHTML = `
                                <img src="${category.icon}" alt="${category.name}">
                                <div>
                                    <span>${category.name}</span>
                                </div>
                            `;

                            categoryDiv.addEventListener('click', function() {
                                // Remove selected class from all categories
                                document.querySelectorAll('.category').forEach(cat => {
                                    cat.classList.remove('selected');
                                    cat.classList.remove('active-category');
                                });

                                // Add selected class to clicked category
                                this.classList.add('selected');
                                this.classList.add('active-category');

                                // Set hidden input values
                                document.getElementById('category-choosed').value = this.dataset.id;
                                document.getElementById('category-id').value = this.dataset.categoryId;
                            });

                            categoriesContainer.appendChild(categoryDiv);
                        });
                    } else {
                        categoriesContainer.innerHTML = '<div class="no-categories">No categories found</div>';
                    }
                } catch (error) {
                    console.error('Error loading categories:', error);
                    document.getElementById('categories-container').innerHTML =
                        '<div class="error-loading">Error loading categories</div>';
                }
            }

            // loadCategorySummary function removed as requested

            async function saveTask(e) {
                e.preventDefault();

                const taskId = document.getElementById('task-id').value;
                const title = document.getElementById('task-title').value;
                const description = document.getElementById('task-description').value;
                const date = document.getElementById('task-date').value;
                const time = document.getElementById('task-time').value;
                const categoryChoosed = document.getElementById('category-choosed').value;
                const category = document.getElementById('category-id').value;

                if (!title || !date || !categoryChoosed) {
                    alert('Please fill in all required fields');
                    return;
                }

                const taskData = {
                    title,
                    description,
                    date,
                    time,
                    categoryChoosed,
                    category
                };

                try {
                    let url = '/tasks';
                    let method = 'POST';

                    if (taskId) {
                        url = `/tasks/${taskId}`;
                        method = 'PUT';
                    }

                    const response = await fetch(url, {
                        method,
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(taskData)
                    });

                    if (!response.ok) {
                        throw new Error('Failed to save task');
                    }

                    // Close form and reload tasks
                    closeTaskForm();
                    loadTasks(categoryFilter.value, statusFilter.value);

                    // Show success message
                    showNotification(taskId ? 'Task updated successfully' : 'Task created successfully', 'success');
                } catch (error) {
                    console.error('Error saving task:', error);
                    showNotification('Error saving task', 'error');
                }
            }

            async function toggleTaskStatus(taskId, currentStatus) {
                try {
                    const response = await fetch(`/tasks/${taskId}/toggle-status`, {
                        method: 'POST'
                    });

                    if (!response.ok) {
                        throw new Error('Failed to update task status');
                    }

                    // Reload tasks
                    loadTasks(categoryFilter.value, statusFilter.value);

                    // Show success message
                    showNotification('Task status updated', 'success');
                } catch (error) {
                    console.error('Error updating task status:', error);
                    showNotification('Error updating task status', 'error');
                }
            }

            async function deleteTask() {
                if (!currentTaskId) return;

                try {
                    const response = await fetch(`/tasks/${currentTaskId}`, {
                        method: 'DELETE'
                    });

                    if (!response.ok) {
                        throw new Error('Failed to delete task');
                    }

                    // Close modal and reload tasks
                    closeDeleteConfirm();
                    loadTasks(categoryFilter.value, statusFilter.value);

                    // Show success message
                    showNotification('Task deleted successfully', 'success');
                } catch (error) {
                    console.error('Error deleting task:', error);
                    showNotification('Error deleting task', 'error');
                }
            }

            function applyFilters() {
                const categoryValue = categoryFilter.value;
                const statusValue = statusFilter.value;
                loadTasks(categoryValue, statusValue);
            }

            function resetFilters() {
                categoryFilter.value = 'all';
                statusFilter.value = 'all';
                loadTasks();
            }

            function showNotification(message, type) {
                const notification = document.createElement('div');
                notification.className = `flash-message flash-${type}`;
                notification.innerHTML = `<i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i> ${message}`;

                const flashMessages = document.querySelector('.flash-messages');
                if (flashMessages) {
                    flashMessages.appendChild(notification);

                    // Add animation
                    notification.style.animation = 'slideIn 0.3s ease, fadeOut 0.3s ease 2.7s';

                    // Remove notification after 3 seconds
                    setTimeout(() => {
                        notification.style.opacity = '0';
                        notification.style.transition = 'opacity 0.5s ease';
                        setTimeout(() => {
                            notification.remove();
                        }, 500);
                    }, 3000);
                } else {
                    console.error('Flash messages container not found');
                }
            }
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set default date to today
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('task-date').value = today;

            // Category selection is now handled by the categories container
            const categoryIdInput = document.getElementById('category-id');
            const categoryChoosedInput = document.getElementById('category-choosed');

            // Set up form submission
            document.getElementById('task-form').addEventListener('submit', function(e) {
                // Check if a category is selected
                if (!categoryChoosedInput.value) {
                    e.preventDefault();
                    alert('Please select a category');
                }
            });

            // Form reset handlers
            document.getElementById('task-form').addEventListener('reset', function() {
                setTimeout(() => {
                    document.getElementById('task-date').value = today;
                    categoryIdInput.value = '';
                    categoryChoosedInput.value = '';

                    // Reset category selections in the categories container
                    document.querySelectorAll('.category').forEach(cat => {
                        cat.classList.remove('selected');
                        cat.classList.remove('active-category');
                    });
                }, 0);
            });
        });
    </script>
