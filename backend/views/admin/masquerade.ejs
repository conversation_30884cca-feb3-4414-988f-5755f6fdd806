<%
// Define the active page for navigation highlighting
const activePage = 'admin';

// Define any additional styles specific to this page
const additionalStyles = `<style>
        /* Responsive Admin Dashboard Styles */

        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background-color: #333;
            color: white;
            padding: 20px 0;
        }

        .sidebar h2 {
            padding: 0 20px;
            margin-bottom: 30px;
        }

        .sidebar ul {
            list-style: none;
            padding: 0;
        }

        .sidebar li {
            padding: 10px 20px;
            border-left: 3px solid transparent;
        }

        .sidebar li.active {
            background-color: #444;
            border-left-color: #4CAF50;
        }

        .sidebar a {
            color: white;
            text-decoration: none;
            display: block;
        }

        .main-content {
            flex: 1;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .user-info {
            display: flex;
            align-items: center;
        }

        .user-info img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .user-list {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .user-list th,
        .user-list td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .user-list th {
            background-color: #f8f8f8;
            font-weight: bold;
        }

        .user-list tr:hover {
            background-color: #f5f5f5;
        }

        .masquerade-btn {
            background-color: #FF9800;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .masquerade-btn:hover {
            background-color: #F57C00;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }

        @media (max-width: 768px) {
            .admin-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .sidebar h2 {
                text-align: center;
            }

            .user-list {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
    `;
%>

<%- include('../partials/admin-header', { 
    title: title, 
    user: user, 
    activePage: activePage, 
    additionalStyles: additionalStyles, 
    messages: messages 
}) %>
    
<div class="main-container">
    <!-- Left Sidebar -->
    <div class="leftside-nav">
        <div class="nav-heading">
            <h5>Admin Panel</h5>
        </div>
        <ul type="none" class="leftside-nav-ul">
            <a href="/admin">
                <li><i class="fas fa-tachometer-alt"></i> Dashboard</li>
            </a>
            <a href="/admin/users" class="active">
                <li><i class="fas fa-users"></i> Users</li>
            </a>
            <a href="/tasks/admin/all">
                <li><i class="fas fa-tasks"></i> All Tasks</li>
            </a>
            <a href="/admin/categories">
                <li><i class="fas fa-tags"></i> Categories</li>
            </a>
            <% if (user.role === 'superadmin') { %>
                <a href="/admin/masquerade">
                    <li><i class="fas fa-user-secret"></i> Masquerade</li>
                </a>
            <% } %>
            <a href="/dashboard">
                <li><i class="fas fa-home"></i> Back to App</li>
            </a>
        </ul>
    </div>

        <div class="main-content">
            

            
            <!-- BreadCumb -->
            <div class="upper-section">
                <div class="greeting-user-section">
                    <h2 class="greeting-user">Masquerade as User</h2>
                    <span>
                        User can be accessed without password
                    </span>
                </div>
            </div>
                
            <!-- User List -->
            <div class="">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> Warning: When masquerading as another user, you will see
                    the application as they would see it. To return to your superadmin account, use the "Stop
                    Masquerading" button that will appear at the top of the page.
                </div>


                <table class="user-list">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <% users.forEach(function(userItem) { %>
                            <tr>
                                <td>
                                    <%= userItem.firstName %>
                                        <%= userItem.lastName %>
                                </td>
                                <td>
                                    <%= userItem.email %>
                                </td>
                                <td>
                                    <%= userItem.role %>
                                </td>
                                <td>
                                    <form action="/admin/masquerade/<%= userItem._id %>" method="POST"
                                        style="display: inline;">
                                        <button type="submit" class="masquerade-btn">
                                            <i class="fas fa-user-secret"></i> Masquerade
                                        </button>
                                    </form>
                                    <span style="font-size: 12px; color: #666; margin-left: 5px;">(<%= userItem.role %>
                                            )</span>
                                </td>
                            </tr>
                            <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
   
        <%- include('../partials/admin-footer', { 
            user: user, 
            additionalScripts: []
        }) %>
    