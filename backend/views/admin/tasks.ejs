<%
// Define the active page for navigation highlighting
const activePage = 'admin';

// Define any additional styles specific to this page
const additionalStyles = `
    <style>
        /* Responsive Admin Dashboard Styles */
        
        .admin-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 250px;
            background-color: #333;
            color: white;
            padding: 20px 0;
        }

        .sidebar h2 {
            padding: 0 20px;
            margin-bottom: 30px;
        }

        .sidebar ul {
            list-style: none;
            padding: 0;
        }

        .sidebar li {
            padding: 10px 20px;
            border-left: 3px solid transparent;
        }

        .sidebar li.active {
            background-color: #444;
            border-left: 3px solid #4CAF50;
        }

        .sidebar a {
            color: white;
            text-decoration: none;
            display: block;
        }

        .sidebar i {
            margin-right: 10px;
        }

        .main-content {
            flex: 1;
            padding: 20px;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .content-header h1 {
            margin: 0;
            color: #333;
        }

        .task-list {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .task-list th,
        .task-list td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .task-list th {
            background-color: #f8f9fa;
            color: #333;
            font-weight: 600;
        }

        .task-list tr:hover {
            background-color: #f5f5f5;
        }

        .badge {
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 600;
        }

        .badge-pending {
            background-color: #FFC107;
            color: #333;
        }

        .badge-completed {
            background-color: #4CAF50;
            color: white;
        }

        .badge-work {
            background-color: #2196F3;
            color: white;
        }

        .badge-personal {
            background-color: #9C27B0;
            color: white;
        }

        .badge-shopping {
            background-color: #FF5722;
            color: white;
        }

        .badge-others {
            background-color: #607D8B;
            color: white;
        }

        .action-btn {
            padding: 5px 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 5px;
        }

        .view-btn {
            background-color: #2196F3;
            color: white;
        }

        .edit-btn {
            background-color: #FFC107;
            color: #333;
        }

        .delete-btn {
            background-color: #F44336;
            color: white;
        }

        footer {
            text-align: center;
            padding: 20px;
            background-color: #333;
            color: white;
            margin-top: 20px;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .admin-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .main-content {
                padding: 10px;
            }

            .task-list {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
    `;
%>

<%- include('../partials/admin-header', { 
    title: title, 
    user: user, 
    activePage: activePage, 
    additionalStyles: additionalStyles, 
    messages: messages 
}) %>

<div class="main-container">
    <!-- Left Sidebar -->
    <div class="leftside-nav">
        <div class="nav-heading">
            <h5>Admin Panel</h5>
        </div>
        <ul type="none" class="leftside-nav-ul">
            <a href="/admin">
                <li><i class="fas fa-tachometer-alt"></i> Dashboard</li>
            </a>
            <a href="/admin/users" class="active">
                <li><i class="fas fa-users"></i> Users</li>
            </a>
            <a href="/tasks/admin/all">
                <li><i class="fas fa-tasks"></i> All Tasks</li>
            </a>
            <a href="/admin/categories">
                <li><i class="fas fa-tags"></i> Categories</li>
            </a>
            <% if (user.role === 'superadmin') { %>
                <a href="/admin/masquerade">
                    <li><i class="fas fa-user-secret"></i> Masquerade</li>
                </a>
            <% } %>
            <a href="/dashboard">
                <li><i class="fas fa-home"></i> Back to App</li>
            </a>
        </ul>
    </div>

    <div class="main-content">

        <!-- BreadCumb -->
        <div class="upper-section">
            <div class="greeting-user-section">
                <h2 class="greeting-user">Category Management</h2>
                <span>
                    View and manage all category in the system
                </span>
            </div>
        </div>
            <div class="content-body">
                <% if (tasks && tasks.length > 0) { %>
                    <table class="task-list">
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Description</th>
                                <th>Category</th>
                                <th>Date</th>
                                <th>User</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <% tasks.forEach(function(task) { %>
                                <tr>
                                    <td><%= task.title %></td>
                                    <td><%= task.description || 'No description' %></td>
                                    <td>
                                        <span class="badge badge-<%= task.categoryChoosed %>">
                                            <%= task.categoryChoosed %>
                                        </span>
                                    </td>
                                    <td><%= task.date %> <%= task.time || '' %></td>
                                    <td>
                                        <% if (task.user) { %>
                                            <%= task.user.firstName %> <%= task.user.lastName %>
                                        <% } else { %>
                                            Unknown User
                                        <% } %>
                                    </td>
                                    <td>
                                        <span class="badge badge-<%= task.completed ? 'completed' : 'pending' %>">
                                            <%= task.completed ? 'Completed' : 'Pending' %>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="/tasks/<%= task._id %>" style="text-decoration: none;">
                                            <button class="action-btn view-btn">
                                                <i class="fas fa-eye"></i> View
                                            </button>
                                        </a>
                                    </td>
                                </tr>
                            <% }); %>
                        </tbody>
                    </table>
                <% } else { %>
                    <div style="text-align: center;padding: 10px;/* background-color: white; */border-radius: 4px;box-shadow: 0 2px 4px rgb(65 65 65);">
                        <p>No tasks found.</p>
                    </div>
                <% } %>
            </div>
        </div>
 
        
        <%- include('../partials/admin-footer', { 
            user: user, 
            additionalScripts: []
        }) %>
    