// Simple test script to verify task API endpoints
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testTaskAPI() {
    console.log('Testing Task API endpoints...');
    
    try {
        // Test GET /api/tasks
        console.log('\n1. Testing GET /api/tasks');
        const response = await fetch(`${BASE_URL}/api/tasks`);
        console.log('Status:', response.status);
        
        if (response.status === 200) {
            const tasks = await response.json();
            console.log('Tasks retrieved:', tasks.length);
        } else {
            console.log('Error:', await response.text());
        }
        
    } catch (error) {
        console.error('Error testing API:', error.message);
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    testTaskAPI();
}

module.exports = { testTaskAPI };
